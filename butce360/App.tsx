/**
 * Butce360 - Personal Finance Management App
 * Pure React Native Implementation
 */

import React, { useEffect } from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';

import { ThemeProvider } from './src/context/ThemeContext';
import { AuthProvider, useAuth } from './src/context/AuthContext';
import MainNavigator from './src/navigation/MainNavigator';
import { useThemedColors } from './src/hooks/useThemedStyles';
// import { GoogleSignin } from '@react-native-google-signin/google-signin';

// Loading screen component
const LoadingScreen: React.FC = () => {
  const colors = useThemedColors();

  return (
    <View style={[loadingStyles.container, { backgroundColor: colors.background.primary }]}>
      <ActivityIndicator size="large" color={colors.primary[500]} />
      <Text style={[loadingStyles.text, { color: colors.text.secondary }]}>
        Yükleniyor...
      </Text>
    </View>
  );
};

// App content component - auth-aware navigation
const AppContent: React.FC = () => {
  const { state, checkAuthStatus } = useAuth();
  const colors = useThemedColors();

  console.log('[App] Auth state:', {
    isLoading: state.isLoading,
    isAuthenticated: state.isAuthenticated,
    isGuest: state.isGuest,
    user: state.user?.email
  });

  // Check auth status on mount (run once)
  useEffect(() => {
    checkAuthStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Show loading screen while checking auth
  if (state.isLoading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer
      theme={{
        dark: colors.background.primary === '#1c1c1e',
        colors: {
          primary: colors.primary[500],
          background: colors.background.primary,
          card: colors.surface.primary,
          text: colors.text.primary,
          border: colors.border.primary,
          notification: colors.accent[500],
        },
        fonts: {
          regular: {
            fontFamily: 'System',
            fontWeight: '400',
          },
          medium: {
            fontFamily: 'System',
            fontWeight: '500',
          },
          bold: {
            fontFamily: 'System',
            fontWeight: '700',
          },
          heavy: {
            fontFamily: 'System',
            fontWeight: '800',
          },
        }
      }}
    >
      <MainNavigator />
    </NavigationContainer>
  );
};

function App() {
  useEffect(() => {
    // Social login configuration will be added later
    console.log('App initialized');
  }, []);

  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <AuthProvider>
          <AppContent />
        </AuthProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}

// Loading screen styles
const loadingStyles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    marginTop: 16,
    fontSize: 16,
  },
});

export default App;
