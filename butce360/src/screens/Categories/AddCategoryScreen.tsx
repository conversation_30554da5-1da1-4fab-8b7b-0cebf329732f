import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Alert,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

import { categoryService } from '../../services/categoryService';

import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

const CATEGORY_ICONS = [
  'fast-food-outline',
  'car-outline',
  'home-outline',
  'bulb-outline',
  'phone-portrait-outline',
  'shirt-outline',
  'film-outline',
  'medical-outline',
  'book-outline',
  'airplane-outline',
  'basket-outline',
  'car-sport-outline',
  'target-outline',
  'medical-outline',
  'musical-notes-outline',
  'fitness-outline',
  'pizza-outline',
  'cafe-outline',
  'game-controller-outline',
  'color-palette-outline',
  'cash-outline',
  'trending-up-outline',
  'briefcase-outline',
  'gift-outline',
  'construct-outline',
  'star-outline',
  'diamond-outline',
  'trophy-outline',
  'bar-chart-outline',
  'card-outline',
];

const CATEGORY_COLORS = [
  '#ef4444',
  '#f97316',
  '#f59e0b',
  '#eab308',
  '#84cc16',
  '#22c55e',
  '#10b981',
  '#14b8a6',
  '#06b6d4',
  '#0ea5e9',
  '#3b82f6',
  '#6366f1',
  '#8b5cf6',
  '#a855f7',
  '#d946ef',
  '#ec4899',
  '#f43f5e',
  '#64748b',
];

interface AddCategoryScreenProps {
  route?: { params?: { categoryId?: string } };
  onNavigate?: (screen: string) => void;
}

const AddCategoryScreen: React.FC<AddCategoryScreenProps> = ({ onNavigate, route }) => {
  const colors = useThemedColors();
  const styles = createStyles(colors);
  const isEditing = !!route?.params?.categoryId;

  const [formData, setFormData] = useState({
    name: '',
    type: 'expense' as 'income' | 'expense',
    icon: 'fast-food-outline',
    color: '#ef4444',
    description: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Kategori adı gerekli';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Kategori adı en az 2 karakter olmalı';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  useEffect(() => {
    const loadCategory = async () => {
      if (!isEditing || !route?.params?.categoryId) return;
      try {
        const cat = await categoryService.getCategoryById(route.params.categoryId);
        if (!cat) return;
        setFormData({
          name: cat.name,
          type: cat.type as any,
          icon: (cat as any).icon || 'fast-food-outline',
          color: (cat as any).color || '#ef4444',
          description: '',
        });
      } catch (e) {
        console.warn('[AddCategoryScreen] failed to prefill category', e);
      }
    };
    loadCategory();
  }, [isEditing, route?.params?.categoryId]);

  const handleSave = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      if (isEditing && route?.params?.categoryId) {
        await categoryService.updateCategory(route.params.categoryId, {
          name: formData.name,
          type: formData.type,
          color: formData.color,
          icon: formData.icon,
        });
      } else {
        // Create category using CategoryService
        await categoryService.createCategory({
          name: formData.name,
          type: formData.type,
          color: formData.color,
          icon: formData.icon,
        });
      }

      Alert.alert('Başarılı', `Kategori ${isEditing ? 'güncellendi' : 'oluşturuldu'}`, [
        { text: 'Tamam', onPress: () => onNavigate?.('categories') },
      ]);
    } catch (error) {
      console.error('Error saving category:', error);
      Alert.alert('Hata', 'Kategori oluşturulurken bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <View
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      <StatusBar
        barStyle={
          colors.background.primary === '#1c1c1e'
            ? 'light-content'
            : 'dark-content'
        }
        backgroundColor={colors.background.primary}
      />
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.content}
          contentContainerStyle={[
            styles.scrollContent,
            {
              paddingBottom: spacing.xl,
            },
          ]}
          showsVerticalScrollIndicator={false}
        >
          {/* Category Preview */}
          <View style={styles.previewSection}>
            <View
              style={[
                styles.categoryPreview,
                { backgroundColor: formData.color },
              ]}
            >
              {CATEGORY_ICONS.includes(formData.icon) ? (
                <Ionicons name={formData.icon} size={32} color="#ffffff" />
              ) : (
                <Text style={{ fontSize: 28, color: '#fff' }}>
                  {formData.icon || '🏷️'}
                </Text>
              )}
            </View>
            <Text style={[styles.previewName, { color: colors.text.primary }]}>
              {formData.name || 'Kategori Adı'}
            </Text>
            <Text
              style={[styles.previewType, { color: colors.text.secondary }]}
            >
              {formData.type === 'income' ? 'Gelir' : 'Gider'} Kategorisi
            </Text>
          </View>

          <View style={styles.formSection}>
            {/* Category Name */}
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
                Kategori Adı *
              </Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    backgroundColor: colors.background.secondary,
                    color: colors.text.primary,
                    borderColor: colors.border.primary,
                  },
                  errors.name && styles.inputError,
                ]}
                value={formData.name}
                onChangeText={value => handleInputChange('name', value)}
                placeholder="Kategori adını girin"
                placeholderTextColor={colors.text.secondary}
              />
              {errors.name && (
                <Text style={[styles.errorText, { color: colors.error[500] }]}>
                  {errors.name}
                </Text>
              )}
            </View>

            {/* Category Type */}
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
                Kategori Türü
              </Text>
              <View style={styles.typeSelector}>
                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    { backgroundColor: colors.background.secondary },
                    formData.type === 'income' && {
                      backgroundColor: colors.primary[500],
                    },
                  ]}
                  onPress={() => handleInputChange('type', 'income')}
                >
                  <Text
                    style={[
                      styles.typeButtonText,
                      { color: colors.text.primary },
                      formData.type === 'income' && {
                        color: colors.background.secondary,
                      },
                    ]}
                  >
                    💰 Gelir
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    { backgroundColor: colors.background.secondary },
                    formData.type === 'expense' && {
                      backgroundColor: colors.primary[500],
                    },
                  ]}
                  onPress={() => handleInputChange('type', 'expense')}
                >
                  <Text
                    style={[
                      styles.typeButtonText,
                      { color: colors.text.primary },
                      formData.type === 'expense' && {
                        color: colors.background.secondary,
                      },
                    ]}
                  >
                    💸 Gider
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Icon Selection */}
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
                İkon Seçin
              </Text>
              <View style={styles.iconGrid}>
                {CATEGORY_ICONS.map((icon, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.iconButton,
                      { backgroundColor: colors.background.secondary },
                      formData.icon === icon && {
                        backgroundColor: colors.primary[500],
                      },
                    ]}
                    onPress={() => handleInputChange('icon', icon)}
                  >
                    <Ionicons
                      name={icon}
                      size={24}
                      color={
                        formData.icon === icon
                          ? colors.background.secondary
                          : colors.text.secondary
                      }
                    />
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Color Selection */}
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
                Renk Seçin
              </Text>
              <View style={styles.colorGrid}>
                {CATEGORY_COLORS.map((color, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.colorButton,
                      { backgroundColor: color },
                      formData.color === color && styles.colorButtonActive,
                    ]}
                    onPress={() => handleInputChange('color', color)}
                  />
                ))}
              </View>
            </View>

            {/* Description */}
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
                Açıklama (Opsiyonel)
              </Text>
              <TextInput
                style={[
                  styles.input,
                  styles.textArea,
                  {
                    backgroundColor: colors.background.secondary,
                    color: colors.text.primary,
                    borderColor: colors.border.primary,
                  },
                ]}
                value={formData.description}
                onChangeText={value => handleInputChange('description', value)}
                placeholder="Kategori hakkında kısa bir açıklama yazın"
                placeholderTextColor={colors.text.secondary}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>
            <TouchableOpacity
              onPress={handleSave}
              style={[
                styles.saveButton,
                { backgroundColor: colors.primary[500] },
                isLoading && styles.saveButtonDisabled,
              ]}
              disabled={isLoading}
            >
              <Text
                style={[
                  styles.saveButtonText,
                  { color: colors.background.secondary },
                ]}
              >
                {isLoading ? 'Kaydediliyor...' : 'Kaydet'}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    keyboardAvoid: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: spacing.screenPadding,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: 'rgba(0,0,0,0.1)',
    },
    backButton: {
      width: 40,
      height: 40,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 20,
    },
    headerTitle: {
      ...typography.styles.title2,
      flex: 1,
      textAlign: 'center',
    },
    saveButton: {
    backgroundColor: colors.primary[500],
    paddingVertical: spacing.buttonPaddingVertical,
    borderRadius: spacing.md,
    alignItems: 'center' as const,
    marginTop: spacing['2xl'],
    minHeight: 48,
  },
    saveButtonDisabled: {
      opacity: 0.6,
    },
    saveButtonText: {
      ...typography.styles.button,
    color: colors.background.secondary,
    },
    content: {
      flex: 1,
    },
    previewSection: {
      alignItems: 'center',
      paddingVertical: spacing.xl,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    },
    categoryPreview: {
      width: 80,
      height: 80,
      borderRadius: 40,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: spacing.md,
    },
    categoryIcon: {
      fontSize: 32,
    },
    previewName: {
      ...typography.styles.h4,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    previewType: {
      ...typography.styles.caption,
      color: colors.text.secondary,
    },
    formSection: {
      padding: spacing.lg,
    },
    inputGroup: {
      marginBottom: spacing.lg,
    },
    inputLabel: {
      ...typography.styles.body1,
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    input: {
      borderWidth: 1,
      borderColor: colors.border.primary,
      borderRadius: spacing.sm,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      fontSize: 16,
      color: colors.text.primary,
      backgroundColor: colors.surface.primary,
    },
    inputError: {
      borderColor: colors.error[500],
    },
    textArea: {
      height: 80,
      paddingTop: spacing.md,
    },
    errorText: {
      ...typography.styles.caption,
      color: colors.error[500],
      marginTop: spacing.xs,
    },
    typeSelector: {
      flexDirection: 'row',
      gap: spacing.sm,
    },
    typeButton: {
      flex: 1,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      borderRadius: spacing.sm,
      borderWidth: 1,
      borderColor: colors.border.primary,
      alignItems: 'center',
    },
    typeButtonActive: {
      backgroundColor: colors.primary[500],
      borderColor: colors.primary[500],
    },
    typeButtonText: {
      ...typography.styles.button,
      color: colors.text.primary,
    },
    typeButtonTextActive: {
      color: colors.surface.primary,
    },
    iconGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: spacing.sm,
    },
    iconButton: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: colors.background.secondary,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: 'transparent',
    },
    iconButtonActive: {
      borderColor: colors.primary[500],
      backgroundColor: colors.primary[50],
    },
    iconButtonText: {
      fontSize: 24,
    },
    colorGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: spacing.sm,
    },
    colorButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      borderWidth: 3,
      borderColor: 'transparent',
    },
    colorButtonActive: {
      borderColor: colors.text.primary,
    },
  });

export default AddCategoryScreen;
