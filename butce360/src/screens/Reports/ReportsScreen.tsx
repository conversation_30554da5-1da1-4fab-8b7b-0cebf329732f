import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  RefreshControl,
  Dimensions,
  Platform,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';



import { useThemedColors, useThemeState } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import { formatCurrency } from '../../utils/formatters';
import { transactionService } from '../../services/transactionService';
import { Transaction } from '../../types/models';

const { width } = Dimensions.get('window');

const ReportsScreen: React.FC = () => {
  const colors = useThemedColors();
  const { isDark } = useThemeState();
  const themedColors = useThemedColors();
  const styles = createStyles(themedColors);
  const [refreshing, setRefreshing] = useState(false);
  // Explicit date range using native picker modal (same pattern as AddTransaction)
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [startDate, setStartDate] = useState<Date>(() => {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), 1);
  });
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [isDatePickerOpen, setDatePickerOpen] = useState(false);
  const [activePicker, setActivePicker] = useState<'start' | 'end' | null>(null);
  const [tempDate, setTempDate] = useState<Date>(new Date());
  const [_loading, setLoading] = useState(true);

  // Fetch transactions
  const fetchTransactions = async () => {
    try {
      setLoading(true);
      const result = await transactionService.getTransactions(1, 1000); // Get all transactions
      setTransactions(result.transactions);
    } catch (error) {
      console.error('[ReportsScreen] Error fetching transactions:', error);
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  };

  // Calculate report data from transactions
  //TODO: check this
  /*
  const calculateReportData = (transactionList: Transaction[], period: 'month' | 'year') => {
    const now = new Date();
    const currentPeriodStart = period === 'month'
      ? new Date(now.getFullYear(), now.getMonth(), 1)
      : new Date(now.getFullYear(), 0, 1);

    const previousPeriodStart = period === 'month'
      ? new Date(now.getFullYear(), now.getMonth() - 1, 1)
      : new Date(now.getFullYear() - 1, 0, 1);

    const previousPeriodEnd = period === 'month'
      ? new Date(now.getFullYear(), now.getMonth(), 0)
      : new Date(now.getFullYear() - 1, 11, 31);

    // Current period transactions
    const currentTransactions = transactionList.filter(t => {
      const transactionDate = new Date(t.transactionDate);
      return transactionDate >= currentPeriodStart;
    });

    // Previous period transactions
    const previousTransactions = transactionList.filter(t => {
      const transactionDate = new Date(t.transactionDate);
      return transactionDate >= previousPeriodStart && transactionDate <= previousPeriodEnd;
    });

    const calculateTotals = (txns: Transaction[]) => {
      const income = txns.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
      const expense = txns.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
      return {
        totalIncome: income,
        totalExpense: expense,
        netIncome: income - expense,
        transactionCount: txns.length,
      };
    };

    return {
      current: calculateTotals(currentTransactions),
      previous: calculateTotals(previousTransactions),
    };
  };
  */

  // Get current report data
  const reportData = (() => {
    const start = startDate;
    const end = endDate;
    const prevEnd = new Date(start.getTime() - 24 * 60 * 60 * 1000);
    const days = Math.max(1, Math.floor((end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000)) + 1);
    const prevStart = new Date(prevEnd.getTime() - (days - 1) * 24 * 60 * 60 * 1000);

    const inRange = transactions.filter(t => {
      const d = new Date(t.transactionDate);
      return d >= start && d <= end;
    });
    const prevRange = transactions.filter(t => {
      const d = new Date(t.transactionDate);
      return d >= prevStart && d <= prevEnd;
    });
    const sum = (tx: Transaction[]) => ({
      totalIncome: tx.filter(t => t.type === 'income').reduce((s, t) => s + t.amount, 0),
      totalExpense: tx.filter(t => t.type === 'expense').reduce((s, t) => s + t.amount, 0),
      netIncome: tx.reduce((s, t) => s + (t.type === 'income' ? t.amount : -t.amount), 0),
      transactionCount: tx.length,
    });
    return { current: sum(inRange), previous: sum(prevRange) };
  })();
  const currentData = reportData.current;
  const previousData = reportData.previous;

  // Calculate category breakdown from current transactions
  const calculateCategoryBreakdown = (transactionList: Transaction[]) => {
    const currentTransactions = transactionList.filter(t => {
      const d = new Date(t.transactionDate);
      return d >= startDate && d <= endDate && t.type === 'expense';
    });

    const categoryTotals: { [key: string]: { amount: number; color: string; icon: string } } = {};

    currentTransactions.forEach(t => {
      const categoryName = t.categoryName || 'Diğer';
      if (!categoryTotals[categoryName]) {
        categoryTotals[categoryName] = {
          amount: 0,
          color: '#95a5a6',
          icon: '📦'
        };
      }
      categoryTotals[categoryName].amount += t.amount;
    });

    const totalExpense = Object.values(categoryTotals).reduce((sum, cat) => sum + cat.amount, 0);

    return Object.entries(categoryTotals)
      .map(([name, data]) => ({
        name,
        amount: data.amount,
        percentage: totalExpense > 0 ? (data.amount / totalExpense) * 100 : 0,
        color: data.color,
        icon: data.icon,
      }))
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 6); // Top 6 categories
  };

  const categoryBreakdown = calculateCategoryBreakdown(transactions);

  // Initial load
  useEffect(() => {
    fetchTransactions();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchTransactions();
    setRefreshing(false);
  };



  const incomeChange = previousData.totalIncome > 0
    ? ((currentData.totalIncome - previousData.totalIncome) / previousData.totalIncome) * 100
    : 0;
  const expenseChange = previousData.totalExpense > 0
    ? ((currentData.totalExpense - previousData.totalExpense) / previousData.totalExpense) * 100
    : 0;
  const netChange = previousData.netIncome !== 0
    ? ((currentData.netIncome - previousData.netIncome) / Math.abs(previousData.netIncome)) * 100
    : 0;

  const openPicker = (which: 'start' | 'end') => {
    setActivePicker(which);
    setTempDate(which === 'start' ? startDate : endDate);
    setDatePickerOpen(true);
  };

  const confirmPicker = () => {
    if (!activePicker) return;
    if (activePicker === 'start') {
      const newStart = tempDate;
      const newEnd = endDate < newStart ? newStart : endDate;
      setStartDate(newStart);
      setEndDate(newEnd);
    } else {
      const newEnd = tempDate;
      const newStart = startDate > newEnd ? newEnd : startDate;
      setEndDate(newEnd);
      setStartDate(newStart);
    }
    setDatePickerOpen(false);
    setActivePicker(null);
  };

  const renderDateRange = () => (
    <View style={styles.dateRangeRow}>
      <View style={styles.dateCol}>
        <Text style={styles.dateFieldLabel}>Başlangıç Tarihi</Text>
        <TouchableOpacity style={styles.dateFilterButton} onPress={() => openPicker('start')}>
          <Text style={styles.dateFilterText}>{startDate.toLocaleDateString('tr-TR')}</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.dateCol}>
        <Text style={styles.dateFieldLabel}>Bitiş Tarihi</Text>
        <TouchableOpacity style={styles.dateFilterButton} onPress={() => openPicker('end')}>
          <Text style={styles.dateFilterText}>{endDate.toLocaleDateString('tr-TR')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Old date picker removed; using modal with explicit confirm

  const renderSummaryCards = () => (
    <View style={styles.summaryContainer}>
      <View style={styles.summaryCard}>
        <Text style={styles.summaryLabel}>Toplam Gelir</Text>
        <Text style={[styles.summaryAmount, styles.incomeAmount]}>
          ₺{formatCurrency(currentData.totalIncome, false)}
        </Text>
        <View style={styles.changeContainer}>
          <Text style={[
            styles.changeText,
            incomeChange >= 0 ? styles.positiveChange : styles.negativeChange
          ]}>
            {incomeChange >= 0 ? '↗' : '↘'} {Math.abs(incomeChange).toFixed(1)}%
          </Text>
          <Text style={styles.changeLabel}>önceki aya göre</Text>
        </View>
      </View>

      <View style={styles.summaryCard}>
        <Text style={styles.summaryLabel}>Toplam Gider</Text>
        <Text style={[styles.summaryAmount, styles.expenseAmount]}>
          ₺{formatCurrency(currentData.totalExpense, false)}
        </Text>
        <View style={styles.changeContainer}>
          <Text style={[
            styles.changeText,
            expenseChange <= 0 ? styles.positiveChange : styles.negativeChange
          ]}>
            {expenseChange >= 0 ? '↗' : '↘'} {Math.abs(expenseChange).toFixed(1)}%
          </Text>
          <Text style={styles.changeLabel}>önceki aya göre</Text>
        </View>
      </View>

      <View style={[styles.summaryCard]}>
        <Text style={styles.summaryLabel}>Net Gelir</Text>
        <Text style={[
          styles.summaryAmount, 
          styles.netAmount,
          currentData.netIncome >= 0 ? styles.incomeAmount : styles.expenseAmount
        ]}>
          ₺{formatCurrency(currentData.netIncome, false)}
        </Text>
        <View style={styles.changeContainer}>
          <Text style={[
            styles.changeText,
            netChange >= 0 ? styles.positiveChange : styles.negativeChange
          ]}>
            {netChange >= 0 ? '↗' : '↘'} {Math.abs(netChange).toFixed(1)}%
          </Text>
          <Text style={styles.changeLabel}>önceki aya göre</Text>
        </View>
      </View>
    </View>
  );

  const renderCategoryBreakdown = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Kategori Dağılımı</Text>
        <TouchableOpacity onPress={() => {}}>
          <Text style={[styles.viewAllText, { color: colors.primary[500] }]}>Detaylar</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.categoryList}>
        {categoryBreakdown.map((category, index) => (
          <View key={index} style={styles.categoryItem}>
            <View style={styles.categoryLeft}>
              <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                <Text style={styles.categoryIconText}>{category.icon}</Text>
              </View>
              <View style={styles.categoryInfo}>
                <Text style={styles.categoryName}>{category.name}</Text>
                <Text style={styles.categoryAmount}>
                  ₺{formatCurrency(category.amount, false)}
                </Text>
              </View>
            </View>
            <View style={styles.categoryRight}>
              <Text style={styles.categoryPercentage}>%{category.percentage.toFixed(2)}</Text>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${category.percentage}%`,
                      backgroundColor: category.color
                    }
                  ]}
                />
              </View>
            </View>
          </View>
        ))}
      </View>
    </View>
  );



  const renderQuickStats = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Hızlı İstatistikler</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{currentData.transactionCount}</Text>
          <Text style={styles.statLabel}>Bu Ay İşlem</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            ₺{formatCurrency(currentData.totalExpense / currentData.transactionCount, false)}
          </Text>
          <Text style={styles.statLabel}>Ortalama Gider</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            {categoryBreakdown[0]?.name || 'Veri Yok'}
          </Text>
          <Text style={styles.statLabel}>En Çok Harcanan</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            {((currentData.netIncome / currentData.totalIncome) * 100).toFixed(0)}%
          </Text>
          <Text style={styles.statLabel}>Tasarruf Oranı</Text>
        </View>
      </View>
    </View>
  );

  // Remove guest check - show reports for all users

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar
        barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background.primary}
      />

      {/* Date Range */}
      {renderDateRange()}

      {/* Content */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderSummaryCards()}
        {renderCategoryBreakdown()}
        {renderQuickStats()}
      </ScrollView>
      
      {/* Date Picker Modal (matches AddTransaction pattern) */}
      {isDatePickerOpen && (
        <View style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0, backgroundColor: '#00000080', justifyContent: 'center', alignItems: 'center' }}>
          <View style={{ width: '90%', borderRadius: 12, backgroundColor: colors.background.secondary, padding: 16 }}>
            <Text style={[styles.sectionTitle, { color: colors.text.primary, marginBottom: 8 }]}>Tarih</Text>
            <DateTimePicker
              value={tempDate}
              mode="date"
              display={Platform.OS === 'ios' ? 'spinner' : 'default'}
              themeVariant={isDark ? 'dark' : 'light'}
              onChange={(_e, d) => { if (d) setTempDate(d); }}
            />
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: 12 }}>
              <TouchableOpacity onPress={() => { setDatePickerOpen(false); setActivePicker(null); }}>
                <Text style={{ color: colors.text.secondary, marginRight: 16 }}>İptal</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={confirmPicker}>
                <Text style={{ color: colors.primary[500], fontWeight: '600' }}>Tamam</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
  },

  // Period Selector
  periodSelector: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 12,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    minHeight: 44,
  },
  periodButtonActive: {},
  periodButtonText: {
    ...typography.styles.button,
    textTransform: 'none',
    fontWeight: '600',
  },
  periodButtonTextActive: {},
  // Active styles
  // (Applied dynamically in render via style arrays)

  // Content
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: spacing['4xl'],
  },

  // Summary Cards
  summaryContainer: {
    paddingHorizontal: spacing.screenPadding,
    marginBottom: spacing.xl,
    gap: spacing.md,
  },
  summaryCard: {
    backgroundColor: colors.background.secondary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  summaryLabel: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    marginBottom: spacing.sm,
  },
  summaryAmount: {
    ...typography.styles.currency,
    fontWeight: '700',
    marginBottom: spacing.sm,
  },
  netAmount: {
    fontSize: 28,
  },
  incomeAmount: {
    color: colors.secondary[600],
  },
  expenseAmount: {
    color: colors.accent[600],
  },
  changeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  changeText: {
    ...typography.styles.caption,
    fontWeight: '600',
  },
  changeLabel: {
    ...typography.styles.caption,
    color: colors.text.tertiary,
  },
  positiveChange: {
    color: colors.secondary[600],
  },
  negativeChange: {
    color: colors.accent[600],
  },

  // Sections
  section: {
    paddingHorizontal: spacing.screenPadding,
    marginBottom: spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
  },
  viewAllText: {
    ...typography.styles.body2,
    color: colors.primary[500],
    fontWeight: '600',
  },

  // Category Breakdown
  categoryList: {
    gap: spacing.md,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  categoryIconText: {
    fontSize: 16,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    ...typography.styles.body2,
    color: colors.text.primary,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  categoryAmount: {
    ...typography.styles.caption,
    color: colors.text.secondary,
  },
  categoryRight: {
    alignItems: 'flex-end',
    minWidth: 80,
  },
  categoryPercentage: {
    ...typography.styles.body2,
    color: colors.text.primary,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  progressBar: {
    width: 60,
    height: 4,
    backgroundColor: colors.background.secondary,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },



  // Stats Grid
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  statItem: {
    backgroundColor: colors.background.secondary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    alignItems: 'center',
    width: (width - spacing.screenPadding * 2 - spacing.md) / 2,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  statValue: {
    ...typography.styles.h5,
    color: colors.text.primary,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  statLabel: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    textAlign: 'center',
  },

  // Date Filter
  dateRangeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.screenPadding,
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
    gap: spacing.md,
  },
  dateCol: { flex: 1, gap: spacing.sm },
  dateFieldLabel: {
    ...typography.styles.body2,
    color: colors.text.primary,
    fontWeight: '600',
    textAlign: 'center',
  },
  dateFilterButton: {
    backgroundColor: colors.background.secondary,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: spacing.cardRadius,
    borderWidth: 1,
    borderColor: colors.border.primary,
  },
  dateFilterText: {
    ...typography.styles.body2,
    color: colors.text.primary,
    textAlign: 'center',
  },

  // Guest State
  guestContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.screenPadding,
  },
  guestIcon: {
    fontSize: 64,
    marginBottom: spacing.xl,
  },
  guestTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  guestText: {
    ...typography.styles.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: spacing['2xl'],
  },
  loginButton: {
    backgroundColor: colors.primary[500],
    paddingVertical: spacing.buttonPaddingVertical,
    paddingHorizontal: spacing.buttonPaddingHorizontal,
    borderRadius: spacing.cardRadius,
  },
  loginButtonText: {
    ...typography.styles.button,
    color: colors.text.inverse,
    textTransform: 'none',
  },
});

export default ReportsScreen;
