import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { spacing } from '../../theme/spacing';
import { authService } from '../../services/authService';
import { apiClient } from '../../services/api';

interface ProfileDetailsScreenProps {
  onNavigate?: (screen: string) => void;
}

interface UserProfile {
  id: string;
  name: string;
  email: string;
  createdAt: string;
  oauthProvider?: string;
}

const ProfileDetailsScreen: React.FC<ProfileDetailsScreenProps> = ({ onNavigate }) => {
  const colors = useThemedColors();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchUserProfile = useCallback(async () => {
    try {
      setLoading(true);
      const token = await authService.getStoredToken();
      
      if (!token) {
        Alert.alert('Hata', 'Oturum bilgisi bulunamadı');
        onNavigate?.('Profile');
        return;
      }

      // /me endpoint'ine istek at
      const { data } = await apiClient.get<any>('/me', {
        'Authorization': `Bearer ${token}`,
      });
      console.log('[ProfileDetails] User profile data:', data);

      // API response format'ına göre data'yı parse et
      const userData = data?.data || data;
      setProfile(userData as UserProfile);
    } catch (error) {
      console.error('[ProfileDetails] Error fetching profile:', error);
      Alert.alert('Hata', 'Profil bilgileri yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [onNavigate]);

  useEffect(() => {
    fetchUserProfile();
  }, [fetchUserProfile]);

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch {
      return dateString;
    }
  };

  const styles = createStyles(colors);

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
          <Text style={[styles.loadingText, { color: colors.text.secondary }]}>Profil bilgileri yükleniyor...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {profile && (
          <View style={styles.profileCard}>
            {/* Profile Icon */}
            <View style={styles.profileIconContainer}>
              <Ionicons name="person-circle" size={80} color={colors.primary[500]} />
            </View>

            {/* Profile Info */}
            <View style={styles.profileInfo}>
              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.text.secondary }]}>Ad Soyad</Text>
                <Text style={[styles.infoValue, { color: colors.text.primary }]}>{profile.name || 'Belirtilmemiş'}</Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.text.secondary }]}>E-posta</Text>
                <Text style={[styles.infoValue, { color: colors.text.primary }]}>{profile.email}</Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.text.secondary }]}>Kullanıcı ID</Text>
                <Text style={[styles.infoValue, { color: colors.text.primary }]}>{profile.id}</Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.text.secondary }]}>Kayıt Tarihi</Text>
                <Text style={[styles.infoValue, { color: colors.text.primary }]}>
                  {profile.createdAt ? formatDate(profile.createdAt) : 'Belirtilmemiş'}
                </Text>
              </View>

              {profile.oauthProvider && (
                <View style={styles.infoItem}>
                  <Text style={[styles.infoLabel, { color: colors.text.secondary }]}>Giriş Yöntemi</Text>
                  <Text style={[styles.infoValue, { color: colors.text.primary }]}>
                    {profile.oauthProvider === 'google' ? 'Google' : 
                     profile.oauthProvider === 'apple' ? 'Apple' : 
                     profile.oauthProvider}
                  </Text>
                </View>
              )}
            </View>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => {
                  console.log('[ProfileDetailsScreen] Edit profile clicked');
                  console.log('[ProfileDetailsScreen] onNavigate function:', onNavigate);
                  onNavigate?.('EditProfile');
                }}
              >
                <Ionicons name="create-outline" size={20} color={colors.background.secondary} />
                <Text style={styles.editButtonText}>Profili Düzenle</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.refreshButton}
                onPress={fetchUserProfile}
              >
                <Ionicons name="refresh-outline" size={20} color={colors.text.secondary} />
                <Text style={[styles.refreshButtonText, { color: colors.text.primary }]}>Yenile</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    content: {
      flex: 1,
      padding: spacing.md,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: { fontSize: 16, fontWeight: '400', marginTop: spacing.sm },
    profileCard: {
      backgroundColor: colors.background.secondary,
      borderRadius: 12,
      padding: spacing.lg,
      shadowColor: colors.neutral[900],
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.3,
      shadowRadius: 3.84,
      elevation: 5,
    },
    profileIconContainer: {
      alignItems: 'center',
      marginBottom: spacing.lg,
    },
    profileInfo: {
      marginBottom: spacing.lg,
    },
    infoItem: {
      marginBottom: spacing.md,
      paddingBottom: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    },
    infoLabel: { fontSize: 12, fontWeight: '400', marginBottom: spacing.xs, textTransform: 'uppercase', letterSpacing: 0.5 },
    infoValue: { fontSize: 16, fontWeight: '500' },
    actionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: spacing.sm,
    },
    editButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.primary[500],
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: 8,
      gap: spacing.xs,
    },
    editButtonText: { fontSize: 16, fontWeight: '600', color: colors.background.secondary },
    refreshButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.background.secondary,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: 8,
      gap: spacing.xs,
    },
    refreshButtonText: { fontSize: 16, fontWeight: '600' },
  });

export default ProfileDetailsScreen;
