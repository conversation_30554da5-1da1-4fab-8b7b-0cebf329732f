import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import InvestmentScreen from '../screens/Investment/InvestmentScreen';
import AddInvestmentSimulationScreen from '../screens/Investment/AddInvestmentSimulationScreen';
import WhatIfComparisonScreen from '../screens/Investment/WhatIfComparisonScreen';
import NavigationHeader from '../components/common/NavigationHeader';
import { useAuth } from '../hooks/useAuth';

export type InvestmentStackParamList = {
  InvestmentHome: undefined;
  AddInvestmentSimulation: undefined;
  WhatIfComparison: undefined;
};

const Stack = createNativeStackNavigator<InvestmentStackParamList>();

const InvestmentStackNavigator: React.FC = () => {
  const { state: authState } = useAuth();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: true,
        header: ({ navigation, back, route }) => {
          const titleMap: Record<string, string> = {
            InvestmentHome: 'Yatırımlarım',
            AddInvestmentSimulation: 'Yatırı<PERSON>',
            WhatIfComparison: 'Ya <PERSON>ö<PERSON>?'
          };
          const title = titleMap[route.name] || 'Yatırım';

          // Only show "Ekle" button if user is authenticated (not guest)
          const right = (route.name === 'InvestmentHome' && authState.isAuthenticated && !authState.isGuest) ? {
            rightButtonText: 'Ekle',
            onRightButtonPress: () => navigation.navigate('AddInvestmentSimulation')
          } : {};

          return (
            <NavigationHeader
              title={title}
              showBackButton={!!back}
              onBackPress={navigation.goBack}
              {...right}
            />
          );
        }
      }}
    >
      <Stack.Screen name="InvestmentHome" component={InvestmentScreen} />
      <Stack.Screen name="AddInvestmentSimulation" component={AddInvestmentSimulationScreen} />
      <Stack.Screen name="WhatIfComparison" component={WhatIfComparisonScreen} />
    </Stack.Navigator>
  );
};

export default InvestmentStackNavigator;
