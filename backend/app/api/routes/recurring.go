package routes

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/nocytech/butce360/pkg/domains/recurring"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/middleware"
	"github.com/nocytech/butce360/pkg/state"
)

func RecurringRoutes(r *gin.RouterGroup, s recurring.Service) {
	g := r.Group("/recurring-transactions")
	g.Use(middleware.Authorized())

	g.POST("", CreateRecurringTransaction(s))
	g.GET("", GetAllRecurringTransactions(s))
	g.GET("/:id", GetRecurringTransactionByID(s))
	g.PUT("/:id", UpdateRecurringTransaction(s))
	g.DELETE("/:id", DeleteRecurringTransaction(s))
}

// @Summary Create recurring transaction
// @Description Create a new recurring transaction
// @Tags recurring-transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dtos.RecurringTransactionRequest true "Recurring transaction data"
// @Success 201 {object} map[string]interface{} "Returns created recurring transaction"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /recurring-transactions [post]
func CreateRecurringTransaction(s recurring.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		fmt.Println("recurison")
		var req dtos.RecurringTransactionRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.CreateRecurringTransaction(userID.String(), &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Get all recurring transactions
// @Description Get all recurring transactions for the current user
// @Tags recurring-transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Returns list of recurring transactions"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /recurring-transactions [get]
func GetAllRecurringTransactions(s recurring.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.GetAllRecurringTransactions(userID.String())
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get recurring transaction by ID
// @Description Get a specific recurring transaction by its ID
// @Tags recurring-transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Recurring Transaction ID"
// @Success 200 {object} map[string]interface{} "Returns recurring transaction details"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /recurring-transactions/{id} [get]
func GetRecurringTransactionByID(s recurring.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		resp, err := s.GetRecurringTransactionByID(id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Update recurring transaction
// @Description Update an existing recurring transaction
// @Tags recurring-transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Recurring Transaction ID"
// @Param request body dtos.RecurringTransactionRequest true "Updated recurring transaction data"
// @Success 200 {object} map[string]interface{} "Returns updated recurring transaction"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /recurring-transactions/{id} [put]
func UpdateRecurringTransaction(s recurring.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		var req dtos.RecurringTransactionRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.UpdateRecurringTransaction(id, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Delete recurring transaction
// @Description Delete a recurring transaction by ID
// @Tags recurring-transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Recurring Transaction ID"
// @Success 200 {object} map[string]interface{} "Success message"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /recurring-transactions/{id} [delete]
func DeleteRecurringTransaction(s recurring.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		if err := s.DeleteRecurringTransaction(id); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   "recurring transaction deleted successfully",
			"status": 200,
		})
	}
}
